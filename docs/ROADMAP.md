# LlamaBot Roadmap!

- [x] - ~~Create assets folder and mount to FastAPI to store assets that can be used in our LLM generated code.~~
- [ ] - Create image_generator_agent workflow to generate images and save them into our assets folder.

- [ ] - Refactor naive workflow implementation into a ReAct agent architecture with tool calling
- [ ] - Add tool calling for CSS file writing + JavaScript file writing
- [ ] - Add chat message history persistence across threads
- [ ] -  Add webpage clone workflow using PlayWright & Vision