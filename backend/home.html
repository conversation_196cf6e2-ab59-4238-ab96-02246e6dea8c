<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LlamaBot Examples</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #121212;
      color: #e0e0e0;
    }
    header {
      text-align: center;
      margin-bottom: 30px;
    }
    .logo {
      width: 120px;
      margin-bottom: 15px;
    }
    h1 {
      color: #ffffff;
      margin-bottom: 10px;
    }
    .subtitle {
      color: #b0b0b0;
      font-size: 1.2rem;
      margin-bottom: 30px;
    }
    .examples-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 20px;
      margin-bottom: 40px;
    }
    .example-card {
      background: #1e1e1e;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.3);
      display: flex;
      flex-direction: column;
    }
    .example-header {
      padding: 15px;
      background-color: #2c5282;
      color: white;
    }
    .example-title {
      margin: 0;
      font-size: 1.2rem;
    }
    .example-iframe {
      width: 100%;
      height: 300px;
      border: none;
    }
    .example-description {
      padding: 15px;
      color: #b0b0b0;
      font-size: 0.9rem;
    }
    .view-full {
      display: block;
      text-align: center;
      margin: 0 15px 15px;
      padding: 8px 15px;
      background-color: #2c5282;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
    }
    .view-full:hover {
      background-color: #3a5d8f;
    }
    footer {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #333;
      color: #b0b0b0;
    }
    footer a {
      color: #63b3ed;
      text-decoration: none;
    }
    footer a:hover {
      text-decoration: underline;
    }
    @media (max-width: 600px) {
      .examples-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <header>
    <img src="https://service-jobs-images.s3.us-east-2.amazonaws.com/7rl98t1weu387r43il97h6ipk1l7" alt="LlamaBot Logo" class="logo">
    <h1>LlamaBot Examples Gallery</h1>
    <p class="subtitle">Explore interactive examples created with LlamaBot</p>
  </header>

  <div class="examples-grid">
    <!-- 3D Hello World Example -->
    <div class="example-card">
      <div class="example-header">
        <h3 class="example-title">3D Hello World</h3>
      </div>
      <iframe class="example-iframe" src="/examples/3d_hello_world.html"></iframe>
      <div class="example-description">
        A simple 3D "Hello World" example using Three.js, showing basic 3D text rendering and animation.
      </div>
      <a href="/examples/3d_hello_world.html" target="_blank" class="view-full">View Full Example</a>
    </div>
    
    <!-- Snake Game Example -->
    <div class="example-card">
      <div class="example-header">
        <h3 class="example-title">3D Snake Game</h3>
      </div>
      <iframe class="example-iframe" src="/examples/3d_snake_game.html"></iframe>
      <div class="example-description">
        A 3D version of the classic Snake game with modern graphics and controls.
      </div>
      <a href="/examples/3d_snake_game.html" target="_blank" class="view-full">View Full Example</a>
    </div>
    
    <!-- LlamaBot Forest Example -->
    <div class="example-card">
      <div class="example-header">
        <h3 class="example-title">3D LlamaBot Forest</h3>
      </div>
      <iframe class="example-iframe" src="/examples/3d_llamabot_forest.html"></iframe>
      <div class="example-description">
        An immersive 3D forest environment featuring LlamaBot characters.
      </div>
      <a href="/examples/3d_llamabot_forest.html" target="_blank" class="view-full">View Full Example</a>
    </div>
  </div>

  <footer>
    <p>Created with LlamaBot - The open-source AI coding agent</p>
    <p><a href="/chat">Go to Chat Interface</a></p>
  </footer>
</body>
</html> 