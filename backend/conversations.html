<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LlamaBot Conversations</title>
    <style>
        /* Dark theme variables */
        :root {
            --bg-color: #1a1a1a;
            --chat-bg: #2d2d2d;
            --text-color: #e0e0e0;
            --input-bg: #3d3d3d;
            --border-color: #404040;
            --button-bg: #4CAF50;
            --button-hover: #45a049;
            --accent-color: #4CAF50;
            --message-user-bg: #3d3d3d;
            --message-ai-bg: #4a4a4a;
            --header-height: 60px;
            --status-color: rgba(76, 175, 80, 0.7);
            --sidebar-bg: #252525;
            --conversation-hover: #333333;
            --conversation-active: #3d3d3d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
        }

        /* Conversations sidebar (left 1/3) */
        .conversations-sidebar {
            width: 33.33%;
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: var(--sidebar-bg);
            border-right: 1px solid var(--border-color);
        }

        .sidebar-header {
            height: var(--header-height);
            padding: 0.8rem 1rem;
            background-color: var(--chat-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .sidebar-header h2 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-color);
        }

        .new-chat-btn {
            padding: 0.5rem 1rem;
            background-color: var(--button-bg);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .new-chat-btn:hover {
            background-color: var(--button-hover);
        }

        .conversations-list {
            flex-grow: 1;
            overflow-y: auto;
            padding: 0.5rem 0;
        }

        .conversation-item {
            padding: 0.8rem 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .conversation-item:hover {
            background-color: var(--conversation-hover);
        }

        .conversation-item.active {
            background-color: var(--conversation-active);
            border-left: 3px solid var(--accent-color);
        }

        .conversation-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: 0.3rem;
            line-height: 1.3;
        }

        .conversation-preview {
            font-size: 0.8rem;
            color: rgba(224, 224, 224, 0.6);
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .conversation-time {
            font-size: 0.75rem;
            color: rgba(224, 224, 224, 0.4);
            margin-top: 0.3rem;
        }

        /* Chat section (right 2/3) */
        .chat-section {
            width: 66.67%;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* Header with logo */
        .chat-header {
            height: var(--header-height);
            padding: 0.8rem 1rem;
            background-color: var(--chat-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .chat-header img {
            width: 32px;
            height: 32px;
            border-radius: 8px;
        }

        .chat-header h1 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-color);
        }

        /* Message history area */
        .message-history {
            flex-grow: 1;
            padding: 1rem;
            overflow-y: auto;
            background-color: var(--chat-bg);
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .message {
            padding: 0.8rem 1rem;
            border-radius: 12px;
            line-height: 1.4;
            max-width: 85%;
            word-wrap: break-word;
            position: relative;
            font-size: 0.95rem;
        }

        .message.user-message {
            background-color: var(--message-user-bg);
            align-self: flex-end;
            border-bottom-right-radius: 4px;
        }

        .message.ai-message {
            background-color: var(--message-ai-bg);
            align-self: flex-start;
            border-bottom-left-radius: 4px;
        }

        /* Input area */
        .input-area {
            padding: 1rem;
            background-color: var(--chat-bg);
            border-top: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
            position: relative;
        }

        .input-area textarea {
            width: 100%;
            padding: 0.8rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--input-bg);
            color: var(--text-color);
            font-size: 0.95rem;
            font-family: inherit;
            transition: border-color 0.2s;
            resize: none;
            min-height: 2.5rem;
            max-height: 12rem;
            overflow-y: auto;
            line-height: 1.2;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .input-area textarea:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .button-row {
            display: flex;
            justify-content: flex-end;
        }

        .input-area button {
            padding: 0.8rem 1.2rem;
            background-color: var(--button-bg);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
            font-weight: 500;
            align-self: flex-end;
        }

        .input-area button:hover {
            background-color: var(--button-hover);
        }

        /* Loading Spinner */
        .loading-spinner {
            display: none;
            margin: 1rem auto;
            width: 30px;
            height: 30px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--accent-color);
            animation: spin 0.8s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Disable input and button while loading */
        .input-area.loading textarea,
        .input-area.loading button {
            opacity: 0.7;
            pointer-events: none;
        }

        /* Streaming message styles */
        .ai-response {
            position: relative;
        }

        .ai-response-content {
            white-space: pre-wrap;
        }
        
        .ai-message-status {
            font-size: 0.7rem;
            color: var(--status-color);
            margin-top: 6px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .ai-message-status svg {
            width: 12px;
            height: 12px;
        }

        /* Typing animation */
        .typing-indicator {
            display: inline-flex;
            align-items: center;
        }
        
        .typing-indicator span {
            width: 4px;
            height: 4px;
            margin: 0 1px;
            background-color: var(--accent-color);
            border-radius: 50%;
            opacity: 0.6;
            animation: bounce 1s infinite;
        }
        
        .typing-indicator span:nth-child(1) { animation-delay: 0s; }
        .typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
        .typing-indicator span:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes bounce {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-4px); }
        }

        /* Workflow node styles */
        .workflow-details {
            margin-top: 6px;
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            user-select: none;
        }

        .workflow-details-content {
            margin-top: 6px;
            padding: 6px;
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            display: none;
        }

        .workflow-details.expanded .workflow-details-content {
            display: block;
        }

        .workflow-node {
            margin-bottom: 5px;
            padding-left: 8px;
            border-left: 2px solid var(--accent-color);
        }

        .node-title {
            font-weight: 500;
            color: var(--accent-color);
        }

        /* Empty state */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            text-align: center;
            color: rgba(224, 224, 224, 0.6);
        }

        .empty-state h3 {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .empty-state p {
            font-size: 0.9rem;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <!-- Conversations Sidebar -->
    <div class="conversations-sidebar">
        <div class="sidebar-header">
            <h2>Conversations</h2>
            <button class="new-chat-btn" onclick="createNewChat()">+ New Chat</button>
        </div>
        <div class="conversations-list" id="conversationsList">
            <!-- Sample conversations - replace with dynamic content -->
            <div class="conversation-item active" onclick="loadConversation('1')">
                <div class="conversation-title">Website Clone Project</div>
                <div class="conversation-preview">Clone the homepage design for...</div>
                <div class="conversation-time">2 hours ago</div>
            </div>
            <div class="conversation-item" onclick="loadConversation('2')">
                <div class="conversation-title">HTML Form Creation</div>
                <div class="conversation-preview">Create a contact form with validation...</div>
                <div class="conversation-time">Yesterday</div>
            </div>
            <div class="conversation-item" onclick="loadConversation('3')">
                <div class="conversation-title">CSS Animation Help</div>
                <div class="conversation-preview">I need help creating smooth transitions...</div>
                <div class="conversation-time">3 days ago</div>
            </div>
        </div>
    </div>

    <!-- Chat Section -->
    <div class="chat-section">
        <div class="chat-header">
            <img src="https://service-jobs-images.s3.us-east-2.amazonaws.com/7rl98t1weu387r43il97h6ipk1l7" alt="LlamaBot Logo">
            <h1>LlamaBot</h1>
        </div>
        <div class="message-history" id="messageHistory">
            <div class="message ai-message">Hi! I'm LlamaBot. How can I help you today?</div>
            <div class="loading-spinner"></div>
        </div>
        <div class="input-area">
            <textarea placeholder="Type your message..." id="messageInput"></textarea>
            <div class="button-row">
                <button onclick="sendMessage()">Send</button>
            </div>
        </div>
    </div>

    <script>
        // Current conversation ID
        let currentConversationId = null;
        
        // Store conversations data
        let conversations = {};

        // Fetch conversations from API
        async function loadConversations() {
            try {
                const response = await fetch('http://localhost:8000/threads');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const conversationsData = await response.json();
                
                // Clear existing conversations
                conversations = {};
                const conversationsList = document.getElementById('conversationsList');
                conversationsList.innerHTML = '';
                
                // If no conversations, show empty state
                if (conversationsData.length === 0) {
                    conversationsList.innerHTML = `
                        <div style="padding: 1rem; text-align: center; color: rgba(224, 224, 224, 0.6);">
                            <p>No conversations yet</p>
                            <p style="font-size: 0.8rem; margin-top: 0.5rem;">Start a new conversation to get started!</p>
                        </div>
                    `;
                    return;
                }
                
                // Process each conversation
                conversationsData.forEach((conv, index) => {
                    const threadId = conv.thread_id;
                    const messages = conv.state[0]?.messages || [];
                    
                    // Generate conversation title and preview
                    const { title, preview } = generateConversationSummary(messages);
                    
                    // Store conversation data
                    conversations[threadId] = {
                        title: title,
                        messages: messages.map(msg => ({
                            type: msg.type === 'human' ? 'user' : 'ai',
                            content: msg.content
                        }))
                    };
                    
                    // Create conversation item
                    const conversationItem = document.createElement('div');
                    conversationItem.className = `conversation-item ${index === 0 ? 'active' : ''}`;
                    conversationItem.onclick = () => loadConversation(threadId);
                    
                    // Calculate relative time
                    const timeAgo = getRelativeTime(conv.state[4] || new Date().toISOString());
                    
                    conversationItem.innerHTML = `
                        <div class="conversation-title">${title}</div>
                        <div class="conversation-preview">${preview}</div>
                        <div class="conversation-time">${timeAgo}</div>
                    `;
                    
                    conversationsList.appendChild(conversationItem);
                    
                    // Load first conversation by default
                    if (index === 0) {
                        currentConversationId = threadId;
                        loadConversationMessages(threadId);
                    }
                });
                
            } catch (error) {
                console.error('Error loading conversations:', error);
                // Show error state
                const conversationsList = document.getElementById('conversationsList');
                conversationsList.innerHTML = `
                    <div style="padding: 1rem; text-align: center; color: rgba(224, 224, 224, 0.6);">
                        <p style="color: #ff6b6b; margin-bottom: 0.5rem;">Error loading conversations</p>
                        <button onclick="loadConversations()" style="margin-top: 0.5rem; padding: 0.5rem 1rem; background: var(--button-bg); color: white; border: none; border-radius: 4px; cursor: pointer;">Retry</button>
                    </div>
                `;
            }
        }

        function generateConversationSummary(messages) {
            if (!messages || messages.length === 0) {
                return { title: 'New Conversation', preview: 'No messages yet...' };
            }
            
            // Find first user message for title
            const firstUserMessage = messages.find(msg => msg.type === 'human');
            let title = 'New Conversation';
            
            if (firstUserMessage && firstUserMessage.content) {
                // Use first 50 characters of first user message as title
                title = firstUserMessage.content.substring(0, 50);
                if (firstUserMessage.content.length > 50) {
                    title += '...';
                }
            }
            
            // Use last message for preview
            const lastMessage = messages[messages.length - 1];
            let preview = 'No messages yet...';
            
            if (lastMessage && lastMessage.content) {
                preview = lastMessage.content.substring(0, 100);
                if (lastMessage.content.length > 100) {
                    preview += '...';
                }
            }
            
            return { title, preview };
        }

        function getRelativeTime(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);
            
            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            if (diffDays < 7) return `${diffDays}d ago`;
            
            return date.toLocaleDateString();
        }

        function createNewChat() {
            // Generate a unique thread ID using timestamp and random component
            const timestamp = Date.now();
            const randomComponent = Math.random().toString(36).substring(2, 8);
            const newId = `thread_${timestamp}_${randomComponent}`;
            
            currentConversationId = newId;
            
            console.log(`Creating new chat with thread ID: ${newId}`);
            
            // Clear current messages
            const messageHistory = document.getElementById('messageHistory');
            messageHistory.innerHTML = `
                <div class="message ai-message">Hi! I'm LlamaBot. How can I help you today?</div>
                <div class="loading-spinner"></div>
            `;
            
            // Add new conversation to sidebar (at the top)
            const conversationsList = document.getElementById('conversationsList');
            const newConversation = document.createElement('div');
            newConversation.className = 'conversation-item active';
            newConversation.onclick = () => loadConversation(newId);
            newConversation.innerHTML = `
                <div class="conversation-title">New Conversation</div>
                <div class="conversation-preview">Start a new conversation...</div>
                <div class="conversation-time">Just now</div>
            `;
            
            // Remove active class from other conversations
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add to the beginning of the list
            conversationsList.insertBefore(newConversation, conversationsList.firstChild);
            
            // Store new conversation
            conversations[newId] = {
                title: 'New Conversation',
                messages: [
                    { type: 'ai', content: 'Hi! I\'m LlamaBot. How can I help you today?' }
                ]
            };
        }

        function loadConversation(conversationId) {
            console.log(`Loading conversation with thread ID: ${conversationId}`);
            
            currentConversationId = conversationId;
            
            // Update active state in sidebar
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Find and activate the clicked conversation item
            const clickedItem = event.target.closest('.conversation-item');
            if (clickedItem) {
                clickedItem.classList.add('active');
            }
            
            loadConversationMessages(conversationId);
        }

        function loadConversationMessages(conversationId) {
            // Load conversation messages
            const conversation = conversations[conversationId];
            if (conversation) {
                const messageHistory = document.getElementById('messageHistory');
                messageHistory.innerHTML = '<div class="loading-spinner"></div>';
                
                // Clear and rebuild messages
                let messagesHtml = '';
                conversation.messages.forEach(message => {
                    const messageClass = message.type === 'user' ? 'user-message' : 'ai-message';
                    // Handle multiline content and escape HTML
                    const content = escapeHtml(message.content).replace(/\n/g, '<br>');
                    messagesHtml += `<div class="message ${messageClass}">${content}</div>`;
                });
                messagesHtml += '<div class="loading-spinner"></div>';
                
                messageHistory.innerHTML = messagesHtml;
                messageHistory.scrollTop = messageHistory.scrollHeight;
            } else {
                // If conversation not found, show empty state
                const messageHistory = document.getElementById('messageHistory');
                messageHistory.innerHTML = `
                    <div class="message ai-message">Hi! I'm LlamaBot. How can I help you today?</div>
                    <div class="loading-spinner"></div>
                `;
            }
        }

        // Helper function to escape HTML to prevent XSS
        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const messageText = input.value.trim();
            
            if (messageText) {
                // Make sure we have a valid thread ID
                if (!currentConversationId) {
                    console.error('No current conversation ID available');
                    return;
                }
                
                console.log(`Sending message to thread ID: ${currentConversationId}`);
                
                const messageHistory = document.querySelector('.message-history');
                const inputArea = document.querySelector('.input-area');
                const spinner = document.querySelector('.loading-spinner');
                
                // Show loading state
                spinner.style.display = 'block';
                inputArea.classList.add('loading');
                
                // Add user message to the UI
                const newMessage = document.createElement('div');
                newMessage.className = 'message user-message';
                newMessage.textContent = messageText;
                messageHistory.appendChild(newMessage);
                
                // Clear input field
                input.value = '';
                
                // Create a placeholder for the AI response
                const aiResponse = document.createElement('div');
                aiResponse.className = 'message ai-message';
                
                // Create the content container
                const responseContent = document.createElement('div');
                responseContent.className = 'ai-response-content';
                responseContent.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
                aiResponse.appendChild(responseContent);
                
                // Create status indicator
                const statusIndicator = document.createElement('div');
                statusIndicator.className = 'ai-message-status';
                statusIndicator.innerHTML = 'Processing...';
                aiResponse.appendChild(statusIndicator);
                
                messageHistory.appendChild(aiResponse);
                
                try {
                    // Set up fetch for streaming with the current thread_id
                    const response = await fetch('http://localhost:8000/chat-message', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            message: messageText,
                            thread_id: currentConversationId  // Pass the current thread ID
                        })
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    // Set up the reader for the stream
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';
                    
                    // Process the stream
                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) {
                            break;
                        }
                        
                        // Decode the chunk and add to buffer
                        buffer += decoder.decode(value, { stream: true });
                        
                        // Process complete lines in the buffer
                        const lines = buffer.split('\n');
                        buffer = lines.pop() || '';
                        
                        for (const line of lines) {
                            if (line.trim() === '') continue;
                            
                            try {
                                const data = JSON.parse(line);
                                
                                if (data.type === 'start') {
                                    statusIndicator.innerHTML = 'Thinking...';
                                } 
                                else if (data.type === 'update') {
                                    // Handle streaming content updates
                                    if (data.node === 'messages') {
                                        const messageChunkMatch = data.value.match(/AIMessageChunk\(content='([^']*)'/);
                                        if (messageChunkMatch) {
                                            const chunkContent = messageChunkMatch[1] ? messageChunkMatch[1].replace(/\\n/g, '\n') : '';
                                            
                                            if (responseContent.querySelector('.typing-indicator')) {
                                                responseContent.innerHTML = '';
                                            }
                                            
                                            responseContent.innerHTML += escapeHtml(chunkContent);
                                            messageHistory.scrollTop = messageHistory.scrollHeight;
                                            return;
                                        }
                                    }
                                    
                                    statusIndicator.innerHTML = `Processing...`;
                                } 
                                else if (data.type === 'final') {
                                    statusIndicator.innerHTML = `
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polyline points="20 6 9 17 4 12"></polyline>
                                        </svg>
                                        Complete
                                    `;
                                    
                                    if (data.messages && data.messages.length > 0) {
                                        const lastMessage = data.messages[data.messages.length - 1];
                                        
                                        if (responseContent.querySelector('.typing-indicator')) {
                                            responseContent.innerHTML = '';
                                        }
                                        
                                        responseContent.innerHTML = escapeHtml(lastMessage.content).replace(/\n/g, '<br>');
                                        
                                        // Update conversation in memory
                                        if (!conversations[currentConversationId]) {
                                            conversations[currentConversationId] = { title: 'New Conversation', messages: [] };
                                        }
                                        conversations[currentConversationId].messages.push(
                                            { type: 'user', content: messageText },
                                            { type: 'ai', content: lastMessage.content }
                                        );
                                        
                                        // Update conversation title if it's still "New Conversation"
                                        if (conversations[currentConversationId].title === 'New Conversation') {
                                            const { title } = generateConversationSummary(conversations[currentConversationId].messages);
                                            conversations[currentConversationId].title = title;
                                            
                                            // Update the sidebar item
                                            const activeItem = document.querySelector('.conversation-item.active .conversation-title');
                                            if (activeItem) {
                                                activeItem.textContent = title;
                                            }
                                        }
                                        
                                        // Update conversation preview
                                        const activePreview = document.querySelector('.conversation-item.active .conversation-preview');
                                        if (activePreview) {
                                            const preview = lastMessage.content.substring(0, 100);
                                            activePreview.textContent = preview + (lastMessage.content.length > 100 ? '...' : '');
                                        }
                                    }
                                }
                                else if (data.type === 'error') {
                                    statusIndicator.innerHTML = `Error: ${data.error}`;
                                    statusIndicator.style.color = '#ff6b6b';
                                }
                            } catch (e) {
                                console.error('Error parsing stream data:', e, line);
                            }
                        }
                    }
                    
                } catch (error) {
                    statusIndicator.innerHTML = `Error: ${error.message}`;
                    statusIndicator.style.color = '#ff6b6b';
                } finally {
                    spinner.style.display = 'none';
                    inputArea.classList.remove('loading');
                    messageHistory.scrollTop = messageHistory.scrollHeight;
                }
            }
        }

        // Allow Enter key to send message, Shift+Enter for new line
        document.getElementById('messageInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                if (e.shiftKey) {
                    return;
                } else {
                    e.preventDefault();
                    sendMessage();
                }
            }
        });

        // Auto-resize textarea as content grows
        document.getElementById('messageInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });

        // Load conversations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadConversations();
        });
    </script>
</body>
</html>