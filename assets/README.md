# Assets Directory

This directory contains assets that LlamaBot can reference and pull into your project. 

## Usage

LlamaBot can access any files stored in this folder by referencing the relative path:
```
/assets/<filename>
```

## Examples

- Images: `/assets/logo.png`
- Stylesheets: `/assets/styles.css` 
- Scripts: `/assets/script.js`
- Documents: `/assets/documentation.pdf`

Simply save your assets in this directory and LlamaBot will be able to incorporate them into your project as needed. 