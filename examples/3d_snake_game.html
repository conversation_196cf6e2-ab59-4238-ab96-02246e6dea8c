<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>3D Continuous First-Person Snake with Smooth Turning</title>
  <style>
    html, body {
      margin: 0; padding: 0;
      width: 100%; height: 100%;
      overflow: hidden;
      background: #f5f5f5;
      font-family: sans-serif;
    }
    #gameCanvas {
      display: block;
      position: absolute;
      top: 0; left: 0;
      width: 100%; height: 100%;
      z-index: 1;
    }
    #scoreBoard {
      position: absolute;
      top: 10px; left: 10px;
      z-index: 2;
      padding: 6px 12px;
      background: rgba(255,255,255,0.8);
      border: 1px solid #333;
      font-size: 1.2rem;
      color: #333;
    }
    #controls {
      position: absolute;
      bottom: 10px; left: 10px;
      z-index: 2;
    }
    #controls button {
      margin: 0 4px;
      padding: 6px 12px;
      font-size: 1rem;
      cursor: pointer;
      border: 1px solid #333;
      background: #fff;
    }
    #controls button:hover {
      background: #eee;
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/three@0.150.0/build/three.min.js"></script>
</head>
<body>
  <canvas id="gameCanvas"></canvas>
  <div id="scoreBoard">Score: 0</div>
  <div id="controls">
    <button id="startBtn">Start</button>
    <button id="pauseBtn">Pause</button>
    <button id="resetBtn">Reset</button>
  </div>

  <script>
  window.addEventListener('load', () => {
    // — Renderer & Scene —
    const canvas = document.getElementById('gameCanvas');
    const renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.autoClear = false;
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xa0a0a0);

    // — Grid Floor —
    const gridSize = 30, blockSize = 1;
    const floor = new THREE.GridHelper(gridSize*blockSize, gridSize, 0x444444, 0x888888);
    floor.position.set((gridSize*blockSize)/2 - blockSize/2, 0, (gridSize*blockSize)/2 - blockSize/2);
    scene.add(floor);

    // — Cameras —
    const mainCamera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
    const halfGrid = (gridSize*blockSize)/2;
    const minimapCamera = new THREE.OrthographicCamera(-halfGrid, halfGrid, halfGrid, -halfGrid, 0.1, 1000);
    minimapCamera.position.set(halfGrid, 50, halfGrid);
    minimapCamera.up.set(0,0,-1);
    minimapCamera.lookAt(halfGrid,0,halfGrid);

    // — Lights —
    scene.add(new THREE.AmbientLight(0xffffff,0.6));
    const dl = new THREE.DirectionalLight(0xffffff,0.6);
    dl.position.set(0,50,0);
    scene.add(dl);

    // — Materials —
    const snakeMaterial = new THREE.MeshLambertMaterial({ color: 0x4caf50 });
    const foodMaterial  = new THREE.MeshLambertMaterial({ color: 0xf44336 });
    const bodyMaterial  = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
    const headMaterial  = new THREE.MeshLambertMaterial({ color: 0xA0522D });
    const earMaterial   = new THREE.MeshLambertMaterial({ color: 0xFFC0CB });
    const eyeMaterial   = new THREE.MeshLambertMaterial({ color: 0x000000 });
    const tailMaterial  = new THREE.MeshLambertMaterial({ color: 0xFFC0CB });

    // — Game Entities: food & mice logic reused —
    let food = { x:0, y:0, mesh:null };
    let mice = [], mouseCount = 3, mouseSpeed = 2.0; // units/sec

    function placeFood() {
      if (food.mesh) scene.remove(food.mesh);
      let fx, fy;
      do {
        fx = Math.floor(Math.random()*gridSize);
        fy = Math.floor(Math.random()*gridSize);
      } while (mice.some(m=>m.x===fx&&m.y===fy));
      food.x = fx; food.y = fy;
      const g = new THREE.BoxGeometry(blockSize*0.8, blockSize*0.8, blockSize*0.8);
      const m = new THREE.Mesh(g, foodMaterial);
      m.position.set(fx*blockSize, blockSize/2, fy*blockSize);
      scene.add(m);
      food.mesh = m;
    }

    function createMouse() {
      const grp = new THREE.Group();
      // body
      const bodyGeo = new THREE.SphereGeometry(blockSize*0.3,12,8);
      const body = new THREE.Mesh(bodyGeo, bodyMaterial);
      body.position.set(0,blockSize*0.3,0);
      grp.add(body);
      // head
      const headGeo = new THREE.SphereGeometry(blockSize*0.2,12,8);
      const head = new THREE.Mesh(headGeo, headMaterial);
      head.position.set(blockSize*0.45, blockSize*0.2, 0);
      grp.add(head);
      // ears
      const earGeo = new THREE.SphereGeometry(blockSize*0.08,8,6);
      const ear1 = new THREE.Mesh(earGeo, earMaterial);
      ear1.position.set(blockSize*0.3, blockSize*0.45, blockSize*0.15);
      const ear2 = ear1.clone();
      ear2.position.z = -blockSize*0.15;
      grp.add(ear1, ear2);
      // eyes
      const eyeGeo = new THREE.SphereGeometry(blockSize*0.04,8,6);
      const eyeL = new THREE.Mesh(eyeGeo, eyeMaterial);
      const eyeR = eyeL.clone();
      eyeL.position.set(blockSize*0.55,blockSize*0.25,blockSize*0.08);
      eyeR.position.set(blockSize*0.55,blockSize*0.25,-blockSize*0.08);
      grp.add(eyeL, eyeR);
      // tail
      const tailGeo = new THREE.CylinderGeometry(blockSize*0.03, blockSize*0.03, blockSize*0.6,8);
      const tail = new THREE.Mesh(tailGeo, tailMaterial);
      tail.rotation.set(0,0,Math.PI/2);
      tail.position.set(-blockSize*0.5,blockSize*0.2,0);
      grp.add(tail);

      let mx, my;
      do {
        mx = Math.floor(Math.random()*gridSize);
        my = Math.floor(Math.random()*gridSize);
      } while ((food.x===mx&&food.y===my) || mice.some(m=>m.x===mx&&m.y===my));
      grp.position.set(mx*blockSize, 0, my*blockSize);
      const angle = Math.random()*Math.PI*2;
      const vx = Math.cos(angle)*mouseSpeed;
      const vy = Math.sin(angle)*mouseSpeed;
      grp.rotation.y = Math.atan2(vy,vx);
      scene.add(grp);
      return { mesh: grp, x: mx, y: my, vx, vy };
    }

    function spawnMice() {
      mice.forEach(m=>scene.remove(m.mesh));
      mice = [];
      for (let i=0;i<mouseCount;i++) mice.push(createMouse());
    }

    function updateMice(dt) {
      mice.forEach(m => {
        const p = m.mesh.position;
        p.x += m.vx * dt;
        p.z += m.vy * dt;
        // bounce
        if (p.x < 0) { p.x = 0; m.vx *= -1; }
        if (p.x > (gridSize-1)*blockSize) { p.x = (gridSize-1)*blockSize; m.vx *= -1; }
        if (p.z < 0) { p.z = 0; m.vy *= -1; }
        if (p.z > (gridSize-1)*blockSize) { p.z = (gridSize-1)*blockSize; m.vy *= -1; }
        // update quantized
        m.x = Math.floor(p.x/blockSize + 0.5);
        m.y = Math.floor(p.z/blockSize + 0.5);
        m.mesh.rotation.y = Math.atan2(m.vy, m.vx);
      });
    }

    // — UI & Score —
    let score = 0;
    const scoreBoard = document.getElementById('scoreBoard');
    function updateScore() {
      scoreBoard.textContent = 'Score: ' + score;
    }
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const resetBtn = document.getElementById('resetBtn');

    // — Continuous Snake State —
    let headPosition = new THREE.Vector3();
    let headingAngle = 0;
    const forwardSpeed = 5;           // units per second
    const turnSpeed    = Math.PI;     // radians per second
    let angularInput   = 0;           // -1..+1
    let isPaused       = true;
    const cameraOffset    = new THREE.Vector3(0, blockSize*0.5, 0);
    const cameraLerpAlpha = 0.1;

    // — Body / Trail Tube —
    let headPositions = [], bodyMesh = null;
    let bodyMaxPoints = 100; // initial trail length in samples

    function initGame() {
      // clean up old body
      if (bodyMesh) {
        scene.remove(bodyMesh);
        bodyMesh.geometry.dispose();
      }
      // initial head at center
      headPosition.set((gridSize/2)*blockSize, blockSize/2, (gridSize/2)*blockSize);
      headingAngle = 0;
      angularInput = 0;
      score = 0; updateScore();
      bodyMaxPoints = 100;
      headPositions = [];
      for (let i=0; i<Math.max(2, bodyMaxPoints); i++) {
        headPositions.push(headPosition.clone());
      }
      // create the initial tube
      const curve = new THREE.CatmullRomCurve3(headPositions);
      const geom = new THREE.TubeGeometry(curve, headPositions.length*2, blockSize*0.45, 8, false);
      bodyMesh = new THREE.Mesh(geom, snakeMaterial);
      scene.add(bodyMesh);
      // spawn food & mice
      placeFood();
      spawnMice();
      // pause at start until "Start" pressed
      isPaused = true;
    }

    // — Input Handling —
    function onKeyDown(e) {
      if (e.keyCode === 37) {           // left arrow
        angularInput = +1;
      } else if (e.keyCode === 39) {    // right arrow
        angularInput = -1;
      } else if (e.keyCode === 32) {    // space = toggle
        isPaused = !isPaused;
      }
    }
    function onKeyUp(e) {
      if (e.keyCode === 37 || e.keyCode === 39) {
        angularInput = 0;
      }
    }
    document.addEventListener('keydown', onKeyDown);
    document.addEventListener('keyup', onKeyUp);
    startBtn.addEventListener('click', ()=>isPaused = false);
    pauseBtn.addEventListener('click', ()=>isPaused = true);
    resetBtn.addEventListener('click', initGame);

    // — Resize Handling —
    function onResize() {
      const w = window.innerWidth, h = window.innerHeight;
      renderer.setSize(w,h);
      mainCamera.aspect = w/h;
      mainCamera.updateProjectionMatrix();
    }
    window.addEventListener('resize', onResize);

    // — Game Over —
    function gameOver() {
      isPaused = true;
      alert('Game Over! Your score: ' + score);
    }

    // — Main Animation Loop —
    let lastTime = performance.now()/1000;
    function animate() {
      requestAnimationFrame(animate);
      const now = performance.now()/1000;
      const deltaTime = now - lastTime;
      lastTime = now;

      if (!isPaused) {
        // 1) Steering
        headingAngle += angularInput * turnSpeed * deltaTime;
        // 2) Move head
        headPosition.x += Math.cos(headingAngle) * forwardSpeed * deltaTime;
        headPosition.z += Math.sin(headingAngle) * forwardSpeed * deltaTime;
        // 3) Boundary check
        if (headPosition.x < 0 || headPosition.x > (gridSize-1)*blockSize ||
            headPosition.z < 0 || headPosition.z > (gridSize-1)*blockSize) {
          gameOver();
        }
        // 4) Update trail samples
        headPositions.push(headPosition.clone());
        if (headPositions.length > bodyMaxPoints) {
          headPositions.shift();
        }
        // 5) Rebuild tube geometry
        const curve = new THREE.CatmullRomCurve3(headPositions);
        bodyMesh.geometry.dispose();
        bodyMesh.geometry = new THREE.TubeGeometry(curve, headPositions.length*2, blockSize*0.45, 8, false);
        // 6) Collision checks (quantize to grid)
        const gx = Math.floor(headPosition.x/blockSize);
        const gy = Math.floor(headPosition.z/blockSize);
        // food
        if (gx === food.x && gy === food.y) {
          score++; updateScore();
          placeFood();
          bodyMaxPoints += 50;
        }
        // mice
        const mi = mice.findIndex(m => m.x === gx && m.y === gy);
        if (mi >= 0) {
          scene.remove(mice[mi].mesh);
          mice.splice(mi,1);
          score += 5; updateScore();
          bodyMaxPoints += 100;
          mice.push(createMouse());
        }
        // 7) Update mice by deltaTime
        updateMice(deltaTime);
      }

      // — Camera smoothing —
      const targetCam = headPosition.clone().add(cameraOffset);
      mainCamera.position.lerp(targetCam, cameraLerpAlpha);
      const forwardVec = new THREE.Vector3(Math.cos(headingAngle), 0, Math.sin(headingAngle));
      const lookTarget = headPosition.clone().add(cameraOffset).add(forwardVec.multiplyScalar(10));
      mainCamera.lookAt(lookTarget);

      // — Render main view —
      renderer.clear();
      const W = window.innerWidth, H = window.innerHeight;
      renderer.setViewport(0,0,W,H);
      renderer.setScissor(0,0,W,H);
      renderer.setScissorTest(true);
      renderer.render(scene, mainCamera);

      // — Render minimap —
      const vw = Math.floor(W/4), vh = Math.floor(H/4);
      const px = W - vw - 10, py = H - vh - 10;
      renderer.setViewport(px,py,vw,vh);
      renderer.setScissor(px,py,vw,vh);
      renderer.render(scene, minimapCamera);
    }

    onResize();
    initGame();
    animate();
  });
  </script>
</body>
</html>