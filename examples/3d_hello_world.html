<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>3D Hello World</title>
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/loaders/FontLoader.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/geometries/TextGeometry.js"></script>
  <style>
    body {
      margin: 0;
      overflow: hidden;
      font-family: Arial, sans-serif;
    }
    #info {
      position: absolute;
      top: 10px;
      width: 100%;
      text-align: center;
      color: white;
      font-size: 24px;
      text-shadow: 1px 1px 2px black;
      z-index: 100;
    }
    #loading {
      position: absolute;
      top: 50%;
      width: 100%;
      text-align: center;
      color: white;
      font-size: 18px;
      transform: translateY(-50%);
    }
  </style>
</head>
<body>
  <div id="info">Hello World in 3D</div>
  <div id="loading">Loading 3D text...</div>

  <script>
    // Scene setup
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x1a1a2e);
    
    // Camera setup
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 15;
    
    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.body.appendChild(renderer.domElement);
    
    // Lights
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(1, 1, 1);
    scene.add(directionalLight);
    
    // Create text
    const fontLoader = new THREE.FontLoader();
    
    fontLoader.load('https://cdn.jsdelivr.net/npm/three@0.132.2/examples/fonts/helvetiker_bold.typeface.json', function(font) {
      // Hide loading message
      document.getElementById('loading').style.display = 'none';
      
      // Create text geometry
      const textGeometry = new THREE.TextGeometry('Hello World', {
        font: font,
        size: 3,
        height: 0.5,
        curveSegments: 12,
        bevelEnabled: true,
        bevelThickness: 0.1,
        bevelSize: 0.05,
        bevelOffset: 0,
        bevelSegments: 5
      });
      
      // Center the text
      textGeometry.computeBoundingBox();
      const textWidth = textGeometry.boundingBox.max.x - textGeometry.boundingBox.min.x;
      
      // Create materials
      const materials = [
        new THREE.MeshPhongMaterial({ color: 0x4285F4, emissive: 0x072534 }), // Blue (front)
        new THREE.MeshPhongMaterial({ color: 0x34A853, emissive: 0x072534 })  // Green (side)
      ];
      
      // Create mesh with materials
      const textMesh = new THREE.Mesh(textGeometry, materials);
      
      // Center text
      textMesh.position.x = -textWidth / 2;
      
      // Add to scene
      scene.add(textMesh);
      
      // Create animation function
      function animate() {
        requestAnimationFrame(animate);
        
        // Rotate text
        textMesh.rotation.y = Math.sin(Date.now() * 0.001) * 0.3;
        textMesh.rotation.x = Math.sin(Date.now() * 0.0005) * 0.2;
        
        // Render
        renderer.render(scene, camera);
      }
      
      // Start animation
      animate();
    });
    
    // Handle window resize
    window.addEventListener('resize', () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    });
  </script>
</body>
</html>