<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>3D Llama Third-Person Game with Cha<PERSON> Bubble</title>

  <!-- Three.js and loaders/controls -->
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/loaders/GLTFLoader.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>

  <style>
    html, body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background-color: #1a1a2e;
      color: #fff;
      font-family: Arial, sans-serif;
    }
    #loading {
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translateX(-50%);
      pointer-events: none;
      text-align: center;
      font-size: 1.2em;
    }
    #loading .spinner {
      display: inline-block;
      width: 1em;
      height: 1em;
      border: 2px solid rgba(255,255,255,0.3);
      border-top: 2px solid #fff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 0.5em;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="loading">
    <span class="spinner"></span> Loading...
  </div>

  <script>
    // ------- GLOBALS -------
    let scene, camera, renderer, llama = null, trees = null, controls;
    let grassTexture, grassNormalMap;
    const loader = new THREE.GLTFLoader();
    loader.setCrossOrigin('anonymous');

    // Chat bubble messages & helper
    const chatMessages = [
      "Welcome to LlamaPress.",
      "My name is LlamaBot.",
      "This guy on top of me is my creator,\nKody. :)"
    ];
    let chatIndex = 0;

    function drawBubble(ctx, text, w, h) {
      const radius = 20;
      const tailWidth = 30;
      const tailHeight = 20;

      // clear
      ctx.clearRect(0, 0, w, h);

      // draw rounded rect + tail
      ctx.fillStyle = 'rgba(255,255,255,0.8)';
      ctx.strokeStyle = '#000';
      ctx.lineWidth = 4;

      ctx.beginPath();
      ctx.moveTo(radius, 0);
      ctx.lineTo(w - radius, 0);
      ctx.arcTo(w, 0, w, radius, radius);
      ctx.lineTo(w, h - radius - tailHeight);
      ctx.arcTo(w, h - tailHeight, w - radius, h - tailHeight, radius);
      ctx.lineTo(w / 2 + tailWidth / 2, h - tailHeight);
      ctx.lineTo(w / 2, h);
      ctx.lineTo(w / 2 - tailWidth / 2, h - tailHeight);
      ctx.lineTo(radius, h - tailHeight);
      ctx.arcTo(0, h - tailHeight, 0, h - radius - tailHeight, radius);
      ctx.lineTo(0, radius);
      ctx.arcTo(0, 0, radius, 0, radius);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();

      // draw multiline text
      const lines = text.split("\n");
      const fontSize = 32;
      const lineHeight = fontSize * 1.2;
      const textBlockHeight = lines.length * lineHeight;
      const startY = (h - tailHeight - textBlockHeight) / 2 + (fontSize / 2);

      ctx.fillStyle = '#000';
      ctx.font = `${fontSize}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      lines.forEach((line, i) => {
        ctx.fillText(line, w / 2, startY + i * lineHeight);
      });
    }

    // Over-the-shoulder offset (initial camera distance)
    const cameraOffset = new THREE.Vector3(2, 3, -6);

    // Control state
    const keys = { forward:false, backward:false, left:false, right:false };

    // UI elements
    const ui = { loading: document.getElementById('loading') };
    function showLoading() { ui.loading.style.display = 'block'; }
    function hideLoading() { ui.loading.style.display = 'none'; }

    // ------- INIT SCENE, CAMERA, RENDERER -------
    function init() {
      scene = new THREE.Scene();
      scene.background = new THREE.Color(0x1a1a2e);

      camera = new THREE.PerspectiveCamera(
        75, window.innerWidth / window.innerHeight, 0.1, 2000
      );
      camera.position.set(cameraOffset.x, cameraOffset.y, cameraOffset.z);

      renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setSize(window.innerWidth, window.innerHeight);
      renderer.shadowMap.enabled = true;
      document.body.appendChild(renderer.domElement);

      // Load grass textures
      const textureLoader = new THREE.TextureLoader();
      grassTexture = textureLoader.load(
        'https://threejsfundamentals.org/threejs/resources/images/grasslight-big.jpg',
        tex => {
          tex.wrapS = tex.wrapT = THREE.RepeatWrapping;
          tex.repeat.set(100, 100);
          tex.anisotropy = renderer.capabilities.getMaxAnisotropy();
        }
      );
      grassNormalMap = textureLoader.load(
        'https://threejsfundamentals.org/threejs/resources/images/grasslight-big-nm.jpg',
        tex => {
          tex.wrapS = tex.wrapT = THREE.RepeatWrapping;
          tex.repeat.set(100, 100);
          tex.anisotropy = renderer.capabilities.getMaxAnisotropy();
        }
      );

      // OrbitControls with zoom
      controls = new THREE.OrbitControls(camera, renderer.domElement);
      controls.target.set(0, 1.5, 0);
      controls.enablePan = false;
      controls.enableZoom = true;
      controls.zoomSpeed  = 1.2;
      controls.minDistance = 3;
      controls.maxDistance = 20;
      controls.minPolarAngle = 0.3;
      controls.maxPolarAngle = Math.PI / 2.2;
      controls.update();

      window.addEventListener('resize', onWindowResize);

      setupScene();
      setupInput();
    }

    // ------- LIGHTS, GRID, GROUND -------
    function setupScene() {
      const ambient = new THREE.AmbientLight(0xffffff, 0.7);
      scene.add(ambient);

      const dir = new THREE.DirectionalLight(0xffffff, 0.9);
      dir.position.set(5, 10, 7.5);
      dir.castShadow = true;
      scene.add(dir);

      const grid = new THREE.GridHelper(2000, 200, 0x444444, 0x222222);
      scene.add(grid);

      const groundMat = new THREE.MeshStandardMaterial({
        map: grassTexture,
        normalMap: grassNormalMap,
        side: THREE.DoubleSide
      });
      const ground = new THREE.Mesh(
        new THREE.PlaneGeometry(1000, 1000),
        groundMat
      );
      ground.rotation.x = -Math.PI / 2;
      ground.receiveShadow = true;
      scene.add(ground);
    }

    // ------- LOAD TREES -------
    function loadTrees() {
      const treeURL = 'https://llamapress-ai-image-uploads.s3.us-west-2.amazonaws.com/jwgl86wbj74wgkup9qfr8rapleq8';
      loader.load(
        treeURL,
        gltf => {
          trees = gltf.scene;
          trees.traverse(node => {
            if (node.isMesh) {
              node.castShadow = true;
              node.receiveShadow = true;
            }
          });
          trees.scale.set(3, 3, 3);
          const groundY = 0;
          const box = new THREE.Box3().setFromObject(trees);
          const offsetY = groundY - box.min.y;
          trees.position.set(5, offsetY, 5);
          scene.add(trees);
        },
        undefined,
        error => console.error('Error loading trees:', error)
      );
    }

    // ------- LOAD LLAMA MODEL -------
    function loadModel(url) {
      showLoading();
      if (llama) {
        scene.remove(llama);
        llama = null;
      }
      loader.load(
        url,
        gltf => {
          llama = gltf.scene;
          llama.scale.set(2, 2, 2);
          llama.position.set(0, 0, 0);
          llama.traverse(node => {
            if (node.isMesh) {
              node.castShadow = true;
              node.receiveShadow = true;
            }
          });

          // Make llama eyes emissive red
          const eyeMat = new THREE.MeshStandardMaterial({
            color: 0x000000, emissive: 0xff0000,
            metalness: 1, roughness: 0.2
          });
          const eyeMesh =
            llama.getObjectByName('Eye_L') ||
            llama.getObjectByName('eye_L') ||
            llama.getObjectByName('Eye') ||
            llama.getObjectByName('eye');
          if (eyeMesh) {
            eyeMesh.material = eyeMat;
          } else {
            const s = new THREE.Mesh(
              new THREE.SphereGeometry(0.1, 32, 32),
              eyeMat
            );
            s.position.set(0.25, 1.8, 0.9);
            llama.add(s);
          }

          // ===== Create chat-bubble sprite =====
          const signCanvas = document.createElement('canvas');
          signCanvas.width = 512;
          signCanvas.height = 256;
          const ctx = signCanvas.getContext('2d');

          // initial draw
          drawBubble(ctx, chatMessages[chatIndex], signCanvas.width, signCanvas.height);

          const signTexture = new THREE.CanvasTexture(signCanvas);
          signTexture.needsUpdate = true;

          const signMaterial = new THREE.SpriteMaterial({
            map: signTexture,
            transparent: true
          });
          const signSprite = new THREE.Sprite(signMaterial);
          signSprite.scale.set(2.5, 1.25, 1);
          signSprite.position.set(0, 2.5, 0.5);
          llama.add(signSprite);

          // rotate through messages every 4 seconds
          setInterval(() => {
            chatIndex = (chatIndex + 1) % chatMessages.length;
            drawBubble(ctx, chatMessages[chatIndex], signCanvas.width, signCanvas.height);
            signTexture.needsUpdate = true;
          }, 4000);
          // ===== End chat-bubble setup =====

          scene.add(llama);
          hideLoading();
        },
        undefined,
        err => {
          console.error('Error loading llama model:', err);
          ui.loading.textContent = 'Failed to load model';
        }
      );
    }

    // ------- INPUT HANDLING -------
    function setupInput() {
      window.addEventListener('keydown', e => {
        switch (e.code) {
          case 'KeyW': keys.forward = true; break;
          case 'KeyS': keys.backward = true; break;
          case 'KeyA': keys.left = true; break;
          case 'KeyD': keys.right = true; break;
          case 'ArrowUp':
            keys.forward = true; e.preventDefault(); break;
          case 'ArrowDown':
            keys.backward = true; e.preventDefault(); break;
          case 'ArrowLeft':
            keys.left = true; e.preventDefault(); break;
          case 'ArrowRight':
            keys.right = true; e.preventDefault(); break;
        }
      });
      window.addEventListener('keyup', e => {
        switch (e.code) {
          case 'KeyW': keys.forward = false; break;
          case 'KeyS': keys.backward = false; break;
          case 'KeyA': keys.left = false; break;
          case 'KeyD': keys.right = false; break;
          case 'ArrowUp':
            keys.forward = false; e.preventDefault(); break;
          case 'ArrowDown':
            keys.backward = false; e.preventDefault(); break;
          case 'ArrowLeft':
            keys.left = false; e.preventDefault(); break;
          case 'ArrowRight':
            keys.right = false; e.preventDefault(); break;
        }
      });
    }

    // ------- GAME LOOP -------
    function animate() {
      requestAnimationFrame(animate);

      if (llama) {
        const turnSpeed = 0.03;
        if (keys.left)  llama.rotation.y += turnSpeed;
        if (keys.right) llama.rotation.y -= turnSpeed;

        const moveSpeed = 0.05;
        const forwardVec = new THREE.Vector3(0, 0, 1)
          .applyQuaternion(llama.quaternion)
          .setY(0)
          .normalize();
        if (keys.forward)  llama.position.addScaledVector(forwardVec, moveSpeed);
        if (keys.backward) llama.position.addScaledVector(forwardVec, -moveSpeed);

        const headPos = llama.position.clone().add(new THREE.Vector3(0, 1.5, 0));
        controls.target.copy(headPos);
      }

      controls.update();
      renderer.render(scene, camera);
    }

    // ------- HANDLE RESIZE -------
    function onWindowResize() {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    }

    // ------- START EVERYTHING -------
    init();
    loadTrees();
    loadModel('https://llamapress-ai-image-uploads.s3.us-west-2.amazonaws.com/03ykjy9ltuekdbfsrm9wyd8rzr8e');
    animate();
  </script>
</body>
</html>