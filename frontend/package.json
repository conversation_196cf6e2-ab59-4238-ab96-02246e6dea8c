{"name": "llamabot-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "axios": "^1.7.2", "@tanstack/react-query": "^5.51.1", "zustand": "^4.5.4", "lucide-react": "^0.424.0", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.40", "tailwindcss": "^3.4.7", "typescript": "^5.2.2", "vite": "^5.3.4"}}