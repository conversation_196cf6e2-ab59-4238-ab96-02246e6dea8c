name: build stars-last-10-days badge
on:
  schedule:                       # run every day at 00:00 UTC
    - cron: '0 0 * * *'
  workflow_dispatch:              # also let you run it manually

jobs:
  update:
    runs-on: ubuntu-latest
    permissions:
      contents: write             # let the workflow commit to the repo
    env:
      # gh CLI will pick this up automatically
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v4

      - name: Compute stars in last 10 days
        run: |
          DAYS=10
          SINCE=$(date -u -d "$DAYS days ago" +%Y-%m-%dT%H:%M:%SZ)

          # GitHub’s “stargazers” endpoint with special media-type
          COUNT=$(gh api \
            -H "Accept: application/vnd.github.star+json" \
            /repos/${{ github.repository }}/stargazers --paginate \
            --jq "[.[] | select(.starred_at > \"$SINCE\")] | length")

          mkdir -p .github/badges
          echo "{\"count\": $COUNT}" > .github/badges/stars_last10.json

      - name: Commit badge data
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>
          git add .github/badges/stars_last10.json
          git commit -m "chore: update stars-last-10-days badge" || echo "No change"
          git push
